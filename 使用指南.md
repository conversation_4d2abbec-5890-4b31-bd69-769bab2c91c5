# 程序集成管理器 - 详细使用指南

## 🎯 快速开始

### 1. 安装和启动
1. **自动安装**：双击 `install.bat` 自动安装依赖
2. **启动程序**：双击 `start.bat` 或运行 `python main.py`

### 2. 第一次使用
1. 程序启动后会显示主界面
2. 左侧是程序列表（初始为空）
3. 右侧是控制面板

## 📋 添加你的程序

### 步骤详解：
1. **点击"➕ 添加程序"按钮**
2. **填写程序信息**：
   - **程序名称**：给你的程序起个名字（如：数据分析工具）
   - **程序路径**：点击"浏览"选择你的程序文件
   - **程序描述**：简单描述程序功能（可选）
3. **点击"确定"**完成添加

### 支持的程序类型：
- ✅ Python脚本（.py文件）
- ✅ 可执行文件（.exe文件）
- ✅ 批处理文件（.bat文件）
- ✅ 其他可执行程序

## 🎥 添加教程视频

### 为什么需要教程？
当你的程序出现错误时，管理器会自动检测错误类型，并弹出对应的教程视频，帮助你快速解决问题。

### 添加教程步骤：
1. **点击"📚 查看教程"按钮**
2. **在教程管理窗口中点击"➕ 添加教程"**
3. **填写教程信息**：
   - **错误类型**：输入错误关键词（如：初始化失败）
   - **教程链接**：输入视频路径或网络链接

### 教程链接格式示例：
```
本地视频文件：
C:\教程\初始化问题解决.mp4
D:\Videos\网络连接修复教程.avi

网络视频链接：
https://www.bilibili.com/video/BV1234567890
https://youtu.be/abcd1234567

本地HTML文件：
file:///C:/tutorials/error_guide.html
```

### 常见错误类型建议：
- `初始化失败` - 程序启动时的问题
- `字节错误` - 数据处理错误
- `内存错误` - 内存不足问题
- `网络连接失败` - 网络相关问题
- `文件不存在` - 文件路径问题
- `权限不足` - 权限相关问题
- `配置错误` - 配置文件问题

## 🔧 程序管理功能

### 运行程序
- **双击程序名称** 或 **点击"▶️ 运行程序"**
- 程序状态会显示为"运行中"
- 管理器会监控程序的输出和错误

### 删除程序
- 选中程序后点击"🗑️ 删除程序"
- 确认删除（不会删除原程序文件，只是从管理器中移除）

### 程序状态说明
- **未运行**：程序已添加但未启动
- **运行中**：程序正在运行
- **已停止**：程序运行结束
- **错误**：程序运行出现错误

## 🔍 更新检测功能

### 自动检测
- 勾选"自动检查更新"，每30分钟自动检查一次
- 通过文件哈希值检测程序是否有变化

### 手动检测
- 点击"🔍 检查更新"立即检查所有程序
- 如果检测到变化会弹出提示

## 🛡️ 错误监控系统

### 工作原理
1. **实时监控**：监控程序的标准输出和错误输出
2. **关键词匹配**：检测错误信息中的关键词
3. **自动弹窗**：匹配到错误类型时自动弹出教程

### 错误检测示例
当程序输出包含以下内容时会触发相应教程：
```
"初始化失败" → 弹出初始化问题教程
"字节错误" → 弹出数据处理教程
"网络连接失败" → 弹出网络问题教程
```

## 🧪 测试功能

### 使用示例程序测试
1. **添加示例程序**：
   - 程序名称：错误演示程序
   - 程序路径：选择 `demo_program.py`
2. **运行示例程序**：会随机产生不同类型的错误
3. **观察错误检测**：看是否弹出相应的教程窗口

### 测试教程播放
1. 在教程管理中选择一个教程
2. 点击"🎥 测试播放"
3. 确认教程能正常打开

## 💡 使用技巧

### 1. 组织你的程序
- 给程序起有意义的名称
- 在描述中写明程序的用途
- 定期检查程序状态

### 2. 制作有效的教程
- 教程要针对具体错误类型
- 视频要清晰易懂
- 可以录制屏幕操作演示

### 3. 错误关键词设置
- 使用具体的错误描述
- 避免过于宽泛的关键词
- 可以设置多个相关关键词

### 4. 备份配置
- 定期备份 `programs_config.json` 文件
- 保存重要的教程视频文件

## 🔧 高级配置

### 配置文件位置
- `programs_config.json` - 主配置文件
- 包含程序列表和教程配置

### 自定义错误检测
你可以手动编辑配置文件添加更多错误类型：
```json
{
  "programs": [...],
  "tutorials": {
    "自定义错误": "C:\\教程\\自定义解决方案.mp4",
    "特殊问题": "https://example.com/special_tutorial.mp4"
  }
}
```

## ❓ 常见问题

### Q: 程序无法启动？
A: 检查Python版本（需要3.7+）和依赖包安装

### Q: 添加的程序无法运行？
A: 确认程序路径正确，程序文件存在且有执行权限

### Q: 教程视频无法播放？
A: 检查视频文件路径或网络链接是否正确

### Q: 错误检测不工作？
A: 确认"启用错误监控"已勾选，错误关键词设置正确

### Q: 如何添加网络视频教程？
A: 直接输入视频的网络链接，如B站、YouTube等链接

## 📞 技术支持

如果遇到问题：
1. 查看控制台错误信息
2. 检查配置文件格式
3. 确认文件路径正确
4. 重启程序尝试

---

**祝你使用愉快！** 🎉

记住：这个工具的核心价值是帮助你更好地管理程序和快速解决问题。花时间设置好教程，会让你的工作效率大大提升！
