@echo off
echo 正在安装程序集成管理器...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到Python，请先安装Python 3.7或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python已安装，正在安装依赖包...
echo.

REM 安装依赖包
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo 依赖包安装失败，尝试使用国内镜像源...
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
)

echo.
echo 安装完成！
echo.
echo 使用方法：
echo 1. 双击运行 main.py 或在命令行输入 python main.py
echo 2. 点击"添加程序"按钮添加你的程序
echo 3. 在"教程管理"中添加错误对应的教程视频
echo.
pause
