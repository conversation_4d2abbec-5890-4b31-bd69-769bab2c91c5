# 程序集成管理器 - 项目结构说明

## 📁 文件结构

```
程序集成管理器/
├── main.py                    # 主程序文件
├── demo_program.py           # 示例程序（用于测试错误检测）
├── requirements.txt          # Python依赖包列表
├── install.bat              # 自动安装脚本
├── start.bat               # 启动脚本
├── README.md               # 项目说明文档
├── 使用指南.md              # 详细使用指南
├── 项目结构说明.md          # 本文件
├── config_example.json     # 配置文件示例
└── programs_config.json    # 实际配置文件（运行后自动生成）
```

## 📄 文件说明

### 核心文件

#### `main.py` - 主程序
- **功能**：程序集成管理器的主要代码
- **包含类**：
  - `ProgramManager` - 主管理器类
  - `ProgramDialog` - 添加程序对话框
  - `TutorialWindow` - 教程管理窗口
  - `AddTutorialDialog` - 添加教程对话框

#### `demo_program.py` - 示例程序
- **功能**：演示错误检测功能
- **特点**：随机产生不同类型的错误
- **用途**：测试集成管理器的错误监控功能

### 配置文件

#### `requirements.txt` - 依赖包
```
tkinter          # GUI界面库
Pillow>=8.0.0   # 图像处理库
requests>=2.25.0 # HTTP请求库
```

#### `programs_config.json` - 配置文件
- **自动生成**：首次运行时自动创建
- **内容**：程序列表和教程配置
- **格式**：JSON格式，便于编辑

### 安装和启动脚本

#### `install.bat` - 安装脚本
- **功能**：自动检查Python环境并安装依赖
- **特点**：支持国内镜像源加速下载
- **适用**：Windows系统

#### `start.bat` - 启动脚本
- **功能**：检查环境并启动程序
- **特点**：友好的错误提示
- **适用**：Windows系统

### 文档文件

#### `README.md` - 项目说明
- **内容**：项目概述、功能介绍、安装说明
- **适合**：快速了解项目

#### `使用指南.md` - 详细指南
- **内容**：详细的使用步骤和技巧
- **适合**：深入学习使用方法

## 🔧 代码结构

### 主要类和方法

#### ProgramManager 类
```python
class ProgramManager:
    def __init__(self)              # 初始化
    def setup_window(self)          # 设置窗口
    def create_widgets(self)        # 创建UI组件
    def add_program(self)           # 添加程序
    def remove_program(self)        # 删除程序
    def run_program(self)           # 运行程序
    def check_updates(self)         # 检查更新
    def check_error_keywords(self)  # 检查错误关键词
    def show_tutorial_popup(self)   # 显示教程弹窗
```

#### 对话框类
- `ProgramDialog` - 添加程序对话框
- `TutorialWindow` - 教程管理窗口
- `AddTutorialDialog` - 添加教程对话框

### 核心功能模块

#### 1. 程序管理模块
- 添加、删除、运行程序
- 程序状态监控
- 版本检测

#### 2. 错误监控模块
- 实时监控程序输出
- 错误关键词匹配
- 自动弹出教程

#### 3. 教程系统模块
- 教程配置管理
- 视频播放功能
- 自定义教程添加

#### 4. 更新检测模块
- 文件哈希比较
- 自动/手动检查
- 版本变化通知

## 🎨 UI设计

### 界面布局
```
┌─────────────────────────────────────────┐
│           🚀 程序集成管理器              │
├─────────────────────────────────────────┤
│  程序列表        │    控制面板          │
│  ┌─────────────┐ │  ┌─────────────────┐ │
│  │程序1 运行中  │ │  │  🔧 程序管理    │ │
│  │程序2 未运行  │ │  │  ➕ 添加程序    │ │
│  │程序3 错误   │ │  │  🗑️ 删除程序    │ │
│  └─────────────┘ │  │  ▶️ 运行程序    │ │
│                  │  └─────────────────┘ │
│                  │  ┌─────────────────┐ │
│                  │  │  🔄 更新检测    │ │
│                  │  │  🔍 检查更新    │ │
│                  │  │  ☑️ 自动检查    │ │
│                  │  └─────────────────┘ │
│                  │  ┌─────────────────┐ │
│                  │  │  🛡️ 错误监控    │ │
│                  │  │  ☑️ 启用监控    │ │
│                  │  │  📚 查看教程    │ │
│                  │  └─────────────────┘ │
├─────────────────────────────────────────┤
│ 状态栏：就绪                            │
└─────────────────────────────────────────┘
```

### 颜色方案
- **主色调**：`#2c3e50` (深蓝灰)
- **成功色**：`#27ae60` (绿色)
- **警告色**：`#f39c12` (橙色)
- **错误色**：`#e74c3c` (红色)
- **信息色**：`#3498db` (蓝色)
- **背景色**：`#f0f0f0` (浅灰)

## 🔄 工作流程

### 程序添加流程
1. 用户点击"添加程序"
2. 弹出添加对话框
3. 用户填写信息并选择文件
4. 验证信息有效性
5. 添加到程序列表
6. 保存配置文件

### 错误检测流程
1. 程序运行时监控输出
2. 检测错误关键词
3. 匹配对应教程
4. 弹出教程窗口
5. 用户观看教程解决问题

### 更新检测流程
1. 计算程序文件哈希值
2. 与保存的版本比较
3. 发现变化时提示用户
4. 更新版本信息

## 📊 数据存储

### 配置文件格式
```json
{
  "programs": [
    {
      "id": 0,
      "name": "程序名称",
      "path": "程序路径",
      "description": "程序描述",
      "status": "运行状态",
      "version": "版本哈希",
      "last_check": "最后检查时间"
    }
  ],
  "tutorials": {
    "错误类型": "教程链接"
  }
}
```

## 🚀 扩展建议

### 可能的功能扩展
1. **日志系统**：记录程序运行日志
2. **定时任务**：支持定时运行程序
3. **远程监控**：支持远程程序监控
4. **插件系统**：支持自定义插件
5. **数据统计**：程序运行统计分析

### 技术改进
1. **数据库存储**：使用SQLite替代JSON
2. **多语言支持**：国际化界面
3. **主题系统**：支持多种UI主题
4. **云同步**：配置云端同步

---

这个项目结构清晰，功能完整，易于维护和扩展。你可以根据需要进行定制和改进！
