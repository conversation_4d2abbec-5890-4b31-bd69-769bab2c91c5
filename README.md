# 程序集成管理器

一个功能强大的程序管理工具，集成了程序管理、更新检测、错误监控和教程系统。

## 🚀 主要功能

### 1. 程序管理
- ➕ **添加程序**：支持添加任意可执行程序（.exe、.py等）
- 🗑️ **删除程序**：安全删除不需要的程序
- ▶️ **运行程序**：一键启动程序，支持后台运行
- 📋 **程序列表**：清晰显示所有程序的状态、版本和检查时间

### 2. 更新检测
- 🔍 **手动检查**：点击按钮立即检查所有程序更新
- 🔄 **自动检查**：每30分钟自动检查程序更新
- 📊 **版本管理**：通过文件哈希检测程序变化

### 3. 错误监控
- 🛡️ **实时监控**：监控程序运行时的错误输出
- 🎯 **关键词检测**：自动识别常见错误类型
- 📚 **智能推荐**：根据错误类型推荐相应教程

### 4. 教程系统
- 🎥 **视频教程**：支持本地视频和网络链接
- 🔧 **自定义教程**：可添加自定义错误类型和对应教程
- 💡 **智能弹窗**：检测到错误时自动弹出相关教程

## 📦 安装说明

### 方法一：自动安装（推荐）
1. 双击运行 `install.bat`
2. 等待自动安装完成

### 方法二：手动安装
1. 确保已安装 Python 3.7+
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

## 🎯 使用教程

### 1. 启动程序
```bash
python main.py
```

### 2. 添加程序
1. 点击右侧"➕ 添加程序"按钮
2. 填写程序名称和选择程序文件
3. 可选填写程序描述
4. 点击"确定"完成添加

### 3. 配置教程
1. 点击"📚 查看教程"按钮
2. 在教程管理窗口中点击"➕ 添加教程"
3. 输入错误类型（如：初始化失败、字节错误等）
4. 输入教程链接（支持本地文件路径或网络链接）

### 4. 运行和监控
1. 双击程序列表中的程序或点击"▶️ 运行程序"
2. 程序会自动监控运行状态和错误
3. 检测到错误时会自动弹出相关教程

## 🔧 高级配置

### 错误关键词配置
程序预设了以下错误关键词：
- `初始化失败`
- `字节错误`
- `内存错误`
- `网络连接失败`
- `文件不存在`

你可以通过教程管理界面添加更多自定义错误类型。

### 配置文件
程序配置保存在 `programs_config.json` 文件中，包含：
- 程序列表信息
- 教程配置
- 其他设置

## 📝 如何添加你的程序和视频

### 添加程序步骤：
1. **准备程序文件**：确保你的程序可以正常运行
2. **打开管理器**：运行 `python main.py`
3. **添加程序**：
   - 点击"➕ 添加程序"
   - 输入程序名称（如：我的数据处理工具）
   - 浏览选择程序文件（.exe、.py等）
   - 填写程序描述（可选）
   - 点击确定

### 添加教程视频步骤：
1. **准备视频文件**：
   - 本地视频：将视频文件放在易访问的位置
   - 网络视频：准备视频的网络链接

2. **配置教程**：
   - 点击"📚 查看教程"
   - 点击"➕ 添加教程"
   - 输入错误类型（如：初始化失败、连接超时等）
   - 输入视频路径或链接：
     - 本地文件：`C:\Videos\tutorial.mp4`
     - 网络链接：`https://example.com/tutorial.mp4`
   - 点击确定

### 示例配置：
```
错误类型：初始化失败
教程链接：C:\教程视频\初始化问题解决.mp4

错误类型：字节错误
教程链接：https://youtu.be/example123

错误类型：网络连接失败
教程链接：file:///C:/tutorials/network_fix.mp4
```

## 🎨 界面特色

- 🎨 **现代化设计**：采用扁平化设计风格
- 🌈 **色彩搭配**：专业的配色方案
- 📱 **响应式布局**：自适应窗口大小
- 🔤 **中文字体**：使用微软雅黑字体，显示清晰

## 🛠️ 技术特性

- **多线程**：程序运行不阻塞UI界面
- **实时监控**：实时监控程序输出和错误
- **自动保存**：配置自动保存到JSON文件
- **错误处理**：完善的异常处理机制
- **跨平台**：支持Windows、Linux、macOS

## 📋 系统要求

- Python 3.7 或更高版本
- tkinter（通常随Python安装）
- Pillow 8.0.0+
- requests 2.25.0+

## 🤝 使用建议

1. **定期备份**：建议定期备份 `programs_config.json` 配置文件
2. **视频格式**：推荐使用 MP4 格式的教程视频
3. **路径设置**：使用绝对路径避免文件找不到的问题
4. **网络教程**：确保网络教程链接的可访问性

## 📞 技术支持

如果在使用过程中遇到问题：
1. 检查Python版本是否符合要求
2. 确认所有依赖包已正确安装
3. 查看控制台输出的错误信息
4. 检查程序路径和教程链接是否正确

---

**享受高效的程序管理体验！** 🎉
