#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例程序 - 用于演示错误检测功能
这个程序会故意产生一些错误来测试集成管理器的错误检测功能
"""

import time
import random
import sys

def main():
    print("=== 示例程序启动 ===")
    print("这是一个用于演示错误检测的示例程序")
    
    # 模拟程序初始化
    print("正在初始化...")
    time.sleep(2)
    
    # 随机选择一个错误场景
    error_scenarios = [
        "normal",           # 正常运行
        "init_failed",      # 初始化失败
        "byte_error",       # 字节错误
        "memory_error",     # 内存错误
        "network_error",    # 网络错误
        "file_not_found"    # 文件不存在
    ]
    
    scenario = random.choice(error_scenarios)
    
    if scenario == "normal":
        print("✅ 程序正常运行")
        for i in range(10):
            print(f"处理数据 {i+1}/10...")
            time.sleep(1)
        print("✅ 程序执行完成")
        
    elif scenario == "init_failed":
        print("❌ 错误：初始化失败 - 无法连接到数据库")
        sys.stderr.write("ERROR: 初始化失败 - Database connection failed\n")
        sys.exit(1)
        
    elif scenario == "byte_error":
        print("❌ 错误：字节错误 - 数据解析失败")
        sys.stderr.write("ERROR: 字节错误 - Invalid byte sequence at position 1024\n")
        sys.exit(1)
        
    elif scenario == "memory_error":
        print("❌ 错误：内存错误 - 内存不足")
        sys.stderr.write("ERROR: 内存错误 - Out of memory while allocating buffer\n")
        sys.exit(1)
        
    elif scenario == "network_error":
        print("❌ 错误：网络连接失败")
        sys.stderr.write("ERROR: 网络连接失败 - Connection timeout after 30 seconds\n")
        sys.exit(1)
        
    elif scenario == "file_not_found":
        print("❌ 错误：文件不存在")
        sys.stderr.write("ERROR: 文件不存在 - config.ini not found\n")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"未预期的错误: {e}")
        sys.stderr.write(f"UNEXPECTED ERROR: {e}\n")
        sys.exit(1)
