@echo off
title 程序集成管理器
echo.
echo ========================================
echo           程序集成管理器 v1.0
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未检测到Python
    echo.
    echo 请先安装Python 3.7或更高版本
    echo 下载地址：https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检测通过
echo.

REM 检查依赖包
echo 正在检查依赖包...
python -c "import tkinter, PIL, requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  检测到缺少依赖包，正在自动安装...
    echo.
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        echo 请手动运行：pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo ✅ 依赖包检查完成
echo.
echo 🚀 正在启动程序集成管理器...
echo.

REM 启动主程序
python main.py

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败
    echo 请检查错误信息并重试
    pause
)

echo.
echo 程序已退出
pause
