#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
程序集成管理器
功能：程序管理、更新检测、错误监控、教程系统
"""

import sys
import os
import json
import subprocess
import threading
import time
import hashlib
import requests
from datetime import datetime
from pathlib import Path

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import tkinter.font as tkFont
from PIL import Image, ImageTk
import webbrowser

class ProgramManager:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.programs = []
        self.config_file = "programs_config.json"
        self.tutorials = {
            "初始化失败": "https://example.com/tutorial/init_failed.mp4",
            "字节错误": "https://example.com/tutorial/byte_error.mp4",
            "内存错误": "https://example.com/tutorial/memory_error.mp4",
            "网络连接失败": "https://example.com/tutorial/network_error.mp4",
            "文件不存在": "https://example.com/tutorial/file_not_found.mp4"
        }
        self.load_config()
        self.create_widgets()
        self.start_monitoring()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("程序集成管理器 v1.0")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # 设置窗口图标和样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Title.TLabel', font=('Microsoft YaHei', 16, 'bold'), background='#f0f0f0')
        style.configure('Heading.TLabel', font=('Microsoft YaHei', 12, 'bold'), background='#f0f0f0')
        style.configure('Custom.TButton', font=('Microsoft YaHei', 10))
        
    def create_widgets(self):
        """创建UI组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', padx=0, pady=0)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🚀 程序集成管理器", 
                              font=('Microsoft YaHei', 18, 'bold'), 
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # 主容器
        main_container = tk.Frame(self.root, bg='#f0f0f0')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 左侧面板 - 程序列表
        left_panel = tk.LabelFrame(main_container, text="📋 程序列表", 
                                  font=('Microsoft YaHei', 12, 'bold'),
                                  bg='#f0f0f0', fg='#2c3e50')
        left_panel.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # 程序列表
        self.program_tree = ttk.Treeview(left_panel, columns=('status', 'version', 'last_check'), 
                                        show='tree headings', height=15)
        self.program_tree.heading('#0', text='程序名称')
        self.program_tree.heading('status', text='状态')
        self.program_tree.heading('version', text='版本')
        self.program_tree.heading('last_check', text='最后检查')
        
        self.program_tree.column('#0', width=200)
        self.program_tree.column('status', width=100)
        self.program_tree.column('version', width=100)
        self.program_tree.column('last_check', width=150)
        
        scrollbar = ttk.Scrollbar(left_panel, orient='vertical', command=self.program_tree.yview)
        self.program_tree.configure(yscrollcommand=scrollbar.set)
        
        self.program_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y')
        
        # 右侧面板 - 控制区域
        right_panel = tk.Frame(main_container, bg='#f0f0f0')
        right_panel.pack(side='right', fill='y', padx=(10, 0))
        
        # 程序管理按钮
        manage_frame = tk.LabelFrame(right_panel, text="🔧 程序管理", 
                                   font=('Microsoft YaHei', 12, 'bold'),
                                   bg='#f0f0f0', fg='#2c3e50')
        manage_frame.pack(fill='x', pady=(0, 10))
        
        tk.Button(manage_frame, text="➕ 添加程序", command=self.add_program,
                 bg='#3498db', fg='white', font=('Microsoft YaHei', 10, 'bold'),
                 relief='flat', padx=20, pady=8).pack(fill='x', padx=10, pady=5)
        
        tk.Button(manage_frame, text="🗑️ 删除程序", command=self.remove_program,
                 bg='#e74c3c', fg='white', font=('Microsoft YaHei', 10, 'bold'),
                 relief='flat', padx=20, pady=8).pack(fill='x', padx=10, pady=5)
        
        tk.Button(manage_frame, text="▶️ 运行程序", command=self.run_program,
                 bg='#27ae60', fg='white', font=('Microsoft YaHei', 10, 'bold'),
                 relief='flat', padx=20, pady=8).pack(fill='x', padx=10, pady=5)
        
        # 更新检测
        update_frame = tk.LabelFrame(right_panel, text="🔄 更新检测", 
                                   font=('Microsoft YaHei', 12, 'bold'),
                                   bg='#f0f0f0', fg='#2c3e50')
        update_frame.pack(fill='x', pady=(0, 10))
        
        tk.Button(update_frame, text="🔍 检查更新", command=self.check_updates,
                 bg='#f39c12', fg='white', font=('Microsoft YaHei', 10, 'bold'),
                 relief='flat', padx=20, pady=8).pack(fill='x', padx=10, pady=5)
        
        self.auto_update_var = tk.BooleanVar(value=True)
        tk.Checkbutton(update_frame, text="自动检查更新", variable=self.auto_update_var,
                      bg='#f0f0f0', font=('Microsoft YaHei', 9)).pack(padx=10, pady=5)
        
        # 错误监控
        monitor_frame = tk.LabelFrame(right_panel, text="🛡️ 错误监控", 
                                    font=('Microsoft YaHei', 12, 'bold'),
                                    bg='#f0f0f0', fg='#2c3e50')
        monitor_frame.pack(fill='x', pady=(0, 10))
        
        self.monitor_var = tk.BooleanVar(value=True)
        tk.Checkbutton(monitor_frame, text="启用错误监控", variable=self.monitor_var,
                      bg='#f0f0f0', font=('Microsoft YaHei', 9)).pack(padx=10, pady=5)
        
        tk.Button(monitor_frame, text="📚 查看教程", command=self.show_tutorials,
                 bg='#9b59b6', fg='white', font=('Microsoft YaHei', 10, 'bold'),
                 relief='flat', padx=20, pady=8).pack(fill='x', padx=10, pady=5)
        
        # 状态栏
        status_frame = tk.Frame(self.root, bg='#34495e', height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(status_frame, text="就绪", 
                                   fg='white', bg='#34495e',
                                   font=('Microsoft YaHei', 9))
        self.status_label.pack(side='left', padx=10, pady=5)
        
        # 绑定事件
        self.program_tree.bind('<Double-1>', self.on_program_double_click)
        
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.programs = data.get('programs', [])
                    self.tutorials.update(data.get('tutorials', {}))
        except Exception as e:
            print(f"加载配置失败: {e}")
            
    def save_config(self):
        """保存配置文件"""
        try:
            data = {
                'programs': self.programs,
                'tutorials': self.tutorials
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败: {e}")

    def add_program(self):
        """添加程序"""
        dialog = ProgramDialog(self.root, "添加程序")
        if dialog.result:
            program_info = dialog.result
            program_info['id'] = len(self.programs)
            program_info['status'] = '未运行'
            program_info['last_check'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            program_info['version'] = self.get_program_version(program_info['path'])

            self.programs.append(program_info)
            self.save_config()
            self.refresh_program_list()
            self.update_status(f"已添加程序: {program_info['name']}")

    def remove_program(self):
        """删除程序"""
        selection = self.program_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的程序")
            return

        item = selection[0]
        program_id = int(self.program_tree.item(item)['values'][0]) if self.program_tree.item(item)['values'] else 0

        if messagebox.askyesno("确认", "确定要删除选中的程序吗？"):
            self.programs = [p for p in self.programs if p.get('id') != program_id]
            self.save_config()
            self.refresh_program_list()
            self.update_status("程序已删除")

    def run_program(self):
        """运行程序"""
        selection = self.program_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要运行的程序")
            return

        item = selection[0]
        program_name = self.program_tree.item(item)['text']
        program = next((p for p in self.programs if p['name'] == program_name), None)

        if program:
            try:
                self.update_status(f"正在启动: {program['name']}")

                # 在新线程中运行程序以避免阻塞UI
                thread = threading.Thread(target=self._run_program_thread, args=(program,))
                thread.daemon = True
                thread.start()

            except Exception as e:
                messagebox.showerror("错误", f"启动程序失败: {str(e)}")
                self.check_error_keywords(str(e))

    def _run_program_thread(self, program):
        """在线程中运行程序"""
        try:
            process = subprocess.Popen(
                program['path'],
                cwd=os.path.dirname(program['path']),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 更新程序状态
            program['status'] = '运行中'
            program['process'] = process
            self.root.after(0, self.refresh_program_list)

            # 监控程序输出
            while process.poll() is None:
                output = process.stdout.readline()
                error = process.stderr.readline()

                if error:
                    self.root.after(0, lambda: self.check_error_keywords(error))

                time.sleep(0.1)

            # 程序结束
            program['status'] = '已停止'
            self.root.after(0, self.refresh_program_list)

        except Exception as e:
            self.root.after(0, lambda: self.check_error_keywords(str(e)))
            program['status'] = '错误'
            self.root.after(0, self.refresh_program_list)

    def check_updates(self):
        """检查程序更新"""
        self.update_status("正在检查更新...")

        for program in self.programs:
            try:
                current_version = self.get_program_version(program['path'])
                if current_version != program.get('version'):
                    program['version'] = current_version
                    program['last_check'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    # 这里可以添加更复杂的版本比较逻辑
                    messagebox.showinfo("更新检测", f"检测到 {program['name']} 有更新")

            except Exception as e:
                print(f"检查 {program['name']} 更新失败: {e}")

        self.save_config()
        self.refresh_program_list()
        self.update_status("更新检查完成")

    def get_program_version(self, program_path):
        """获取程序版本（通过文件哈希）"""
        try:
            with open(program_path, 'rb') as f:
                file_hash = hashlib.md5(f.read()).hexdigest()[:8]
                return file_hash
        except:
            return "未知"

    def check_error_keywords(self, error_text):
        """检查错误关键词并弹出相应教程"""
        if not self.monitor_var.get():
            return

        error_text = error_text.lower()

        for keyword, tutorial_url in self.tutorials.items():
            if keyword.lower() in error_text:
                self.show_tutorial_popup(keyword, tutorial_url)
                break

    def show_tutorial_popup(self, error_type, tutorial_url):
        """显示教程弹窗"""
        popup = tk.Toplevel(self.root)
        popup.title(f"错误教程 - {error_type}")
        popup.geometry("400x300")
        popup.configure(bg='#f0f0f0')

        # 居中显示
        popup.transient(self.root)
        popup.grab_set()

        # 错误信息
        tk.Label(popup, text=f"检测到错误: {error_type}",
                font=('Microsoft YaHei', 14, 'bold'),
                bg='#f0f0f0', fg='#e74c3c').pack(pady=20)

        tk.Label(popup, text="为您推荐相关教程:",
                font=('Microsoft YaHei', 12),
                bg='#f0f0f0').pack(pady=10)

        # 教程链接按钮
        tk.Button(popup, text="🎥 观看视频教程",
                 command=lambda: webbrowser.open(tutorial_url),
                 bg='#3498db', fg='white',
                 font=('Microsoft YaHei', 12, 'bold'),
                 relief='flat', padx=30, pady=10).pack(pady=20)

        tk.Button(popup, text="关闭", command=popup.destroy,
                 bg='#95a5a6', fg='white',
                 font=('Microsoft YaHei', 10),
                 relief='flat', padx=20, pady=5).pack(pady=10)

    def show_tutorials(self):
        """显示教程管理窗口"""
        tutorial_window = TutorialWindow(self.root, self.tutorials)

    def refresh_program_list(self):
        """刷新程序列表显示"""
        # 清空现有项目
        for item in self.program_tree.get_children():
            self.program_tree.delete(item)

        # 添加程序项目
        for program in self.programs:
            self.program_tree.insert('', 'end',
                                   text=program['name'],
                                   values=(program['status'],
                                          program.get('version', '未知'),
                                          program.get('last_check', '从未')))

    def on_program_double_click(self, event):
        """双击程序项目事件"""
        selection = self.program_tree.selection()
        if selection:
            self.run_program()

    def start_monitoring(self):
        """开始监控循环"""
        def monitor_loop():
            while True:
                if self.auto_update_var.get():
                    # 每30分钟自动检查一次更新
                    self.root.after(0, self.check_updates)
                time.sleep(1800)  # 30分钟

        monitor_thread = threading.Thread(target=monitor_loop)
        monitor_thread.daemon = True
        monitor_thread.start()

    def update_status(self, message):
        """更新状态栏"""
        self.status_label.config(text=f"{datetime.now().strftime('%H:%M:%S')} - {message}")

    def run(self):
        """运行主程序"""
        self.refresh_program_list()
        self.root.mainloop()


class ProgramDialog:
    """添加程序对话框"""
    def __init__(self, parent, title):
        self.result = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.configure(bg='#f0f0f0')
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.create_widgets()

    def create_widgets(self):
        """创建对话框组件"""
        main_frame = tk.Frame(self.dialog, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # 程序名称
        tk.Label(main_frame, text="程序名称:", font=('Microsoft YaHei', 10, 'bold'),
                bg='#f0f0f0').pack(anchor='w', pady=(0, 5))
        self.name_entry = tk.Entry(main_frame, font=('Microsoft YaHei', 10), width=50)
        self.name_entry.pack(fill='x', pady=(0, 15))

        # 程序路径
        tk.Label(main_frame, text="程序路径:", font=('Microsoft YaHei', 10, 'bold'),
                bg='#f0f0f0').pack(anchor='w', pady=(0, 5))

        path_frame = tk.Frame(main_frame, bg='#f0f0f0')
        path_frame.pack(fill='x', pady=(0, 15))

        self.path_entry = tk.Entry(path_frame, font=('Microsoft YaHei', 10))
        self.path_entry.pack(side='left', fill='x', expand=True)

        tk.Button(path_frame, text="浏览", command=self.browse_file,
                 bg='#3498db', fg='white', font=('Microsoft YaHei', 9),
                 relief='flat').pack(side='right', padx=(10, 0))

        # 描述
        tk.Label(main_frame, text="程序描述:", font=('Microsoft YaHei', 10, 'bold'),
                bg='#f0f0f0').pack(anchor='w', pady=(0, 5))
        self.desc_text = tk.Text(main_frame, height=6, font=('Microsoft YaHei', 10))
        self.desc_text.pack(fill='both', expand=True, pady=(0, 15))

        # 按钮
        button_frame = tk.Frame(main_frame, bg='#f0f0f0')
        button_frame.pack(fill='x')

        tk.Button(button_frame, text="确定", command=self.ok_clicked,
                 bg='#27ae60', fg='white', font=('Microsoft YaHei', 10, 'bold'),
                 relief='flat', padx=30, pady=8).pack(side='right', padx=(10, 0))

        tk.Button(button_frame, text="取消", command=self.cancel_clicked,
                 bg='#95a5a6', fg='white', font=('Microsoft YaHei', 10),
                 relief='flat', padx=30, pady=8).pack(side='right')

    def browse_file(self):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title="选择程序文件",
            filetypes=[("可执行文件", "*.exe"), ("Python文件", "*.py"), ("所有文件", "*.*")]
        )
        if filename:
            self.path_entry.delete(0, tk.END)
            self.path_entry.insert(0, filename)

            # 自动填充程序名称
            if not self.name_entry.get():
                name = os.path.splitext(os.path.basename(filename))[0]
                self.name_entry.insert(0, name)

    def ok_clicked(self):
        """确定按钮点击"""
        name = self.name_entry.get().strip()
        path = self.path_entry.get().strip()
        desc = self.desc_text.get(1.0, tk.END).strip()

        if not name or not path:
            messagebox.showerror("错误", "请填写程序名称和路径")
            return

        if not os.path.exists(path):
            messagebox.showerror("错误", "程序文件不存在")
            return

        self.result = {
            'name': name,
            'path': path,
            'description': desc
        }
        self.dialog.destroy()

    def cancel_clicked(self):
        """取消按钮点击"""
        self.dialog.destroy()


class TutorialWindow:
    """教程管理窗口"""
    def __init__(self, parent, tutorials):
        self.tutorials = tutorials

        self.window = tk.Toplevel(parent)
        self.window.title("教程管理")
        self.window.geometry("600x500")
        self.window.configure(bg='#f0f0f0')
        self.window.transient(parent)

        self.create_widgets()

    def create_widgets(self):
        """创建教程管理界面"""
        main_frame = tk.Frame(self.window, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # 标题
        tk.Label(main_frame, text="📚 错误教程管理",
                font=('Microsoft YaHei', 16, 'bold'),
                bg='#f0f0f0', fg='#2c3e50').pack(pady=(0, 20))

        # 教程列表
        list_frame = tk.LabelFrame(main_frame, text="现有教程",
                                 font=('Microsoft YaHei', 12, 'bold'),
                                 bg='#f0f0f0')
        list_frame.pack(fill='both', expand=True, pady=(0, 20))

        # 创建教程列表
        self.tutorial_tree = ttk.Treeview(list_frame, columns=('url',), show='tree headings')
        self.tutorial_tree.heading('#0', text='错误类型')
        self.tutorial_tree.heading('url', text='教程链接')

        self.tutorial_tree.column('#0', width=200)
        self.tutorial_tree.column('url', width=350)

        scrollbar2 = ttk.Scrollbar(list_frame, orient='vertical', command=self.tutorial_tree.yview)
        self.tutorial_tree.configure(yscrollcommand=scrollbar2.set)

        self.tutorial_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar2.pack(side='right', fill='y')

        # 刷新教程列表
        self.refresh_tutorial_list()

        # 操作按钮
        button_frame = tk.Frame(main_frame, bg='#f0f0f0')
        button_frame.pack(fill='x')

        tk.Button(button_frame, text="➕ 添加教程", command=self.add_tutorial,
                 bg='#3498db', fg='white', font=('Microsoft YaHei', 10, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=(0, 10))

        tk.Button(button_frame, text="🗑️ 删除教程", command=self.remove_tutorial,
                 bg='#e74c3c', fg='white', font=('Microsoft YaHei', 10, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='left', padx=(0, 10))

        tk.Button(button_frame, text="🎥 测试播放", command=self.test_tutorial,
                 bg='#9b59b6', fg='white', font=('Microsoft YaHei', 10, 'bold'),
                 relief='flat', padx=20, pady=8).pack(side='left')

    def refresh_tutorial_list(self):
        """刷新教程列表"""
        for item in self.tutorial_tree.get_children():
            self.tutorial_tree.delete(item)

        for error_type, url in self.tutorials.items():
            self.tutorial_tree.insert('', 'end', text=error_type, values=(url,))

    def add_tutorial(self):
        """添加教程"""
        dialog = AddTutorialDialog(self.window)
        if dialog.result:
            error_type, url = dialog.result
            self.tutorials[error_type] = url
            self.refresh_tutorial_list()

    def remove_tutorial(self):
        """删除教程"""
        selection = self.tutorial_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的教程")
            return

        item = selection[0]
        error_type = self.tutorial_tree.item(item)['text']

        if messagebox.askyesno("确认", f"确定要删除 '{error_type}' 的教程吗？"):
            del self.tutorials[error_type]
            self.refresh_tutorial_list()

    def test_tutorial(self):
        """测试播放教程"""
        selection = self.tutorial_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要测试的教程")
            return

        item = selection[0]
        url = self.tutorial_tree.item(item)['values'][0]
        webbrowser.open(url)


class AddTutorialDialog:
    """添加教程对话框"""
    def __init__(self, parent):
        self.result = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("添加教程")
        self.dialog.geometry("400x250")
        self.dialog.configure(bg='#f0f0f0')
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_widgets()

    def create_widgets(self):
        """创建对话框组件"""
        main_frame = tk.Frame(self.dialog, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # 错误类型
        tk.Label(main_frame, text="错误类型:", font=('Microsoft YaHei', 10, 'bold'),
                bg='#f0f0f0').pack(anchor='w', pady=(0, 5))
        self.error_entry = tk.Entry(main_frame, font=('Microsoft YaHei', 10), width=40)
        self.error_entry.pack(fill='x', pady=(0, 15))

        # 教程链接
        tk.Label(main_frame, text="教程链接:", font=('Microsoft YaHei', 10, 'bold'),
                bg='#f0f0f0').pack(anchor='w', pady=(0, 5))
        self.url_entry = tk.Entry(main_frame, font=('Microsoft YaHei', 10), width=40)
        self.url_entry.pack(fill='x', pady=(0, 15))

        # 提示
        tk.Label(main_frame, text="提示：可以是本地视频文件路径或网络链接",
                font=('Microsoft YaHei', 8), fg='#7f8c8d',
                bg='#f0f0f0').pack(anchor='w', pady=(0, 20))

        # 按钮
        button_frame = tk.Frame(main_frame, bg='#f0f0f0')
        button_frame.pack(fill='x')

        tk.Button(button_frame, text="确定", command=self.ok_clicked,
                 bg='#27ae60', fg='white', font=('Microsoft YaHei', 10, 'bold'),
                 relief='flat', padx=30, pady=8).pack(side='right', padx=(10, 0))

        tk.Button(button_frame, text="取消", command=self.cancel_clicked,
                 bg='#95a5a6', fg='white', font=('Microsoft YaHei', 10),
                 relief='flat', padx=30, pady=8).pack(side='right')

    def ok_clicked(self):
        """确定按钮点击"""
        error_type = self.error_entry.get().strip()
        url = self.url_entry.get().strip()

        if not error_type or not url:
            messagebox.showerror("错误", "请填写错误类型和教程链接")
            return

        self.result = (error_type, url)
        self.dialog.destroy()

    def cancel_clicked(self):
        """取消按钮点击"""
        self.dialog.destroy()


if __name__ == "__main__":
    try:
        app = ProgramManager()
        app.run()
    except Exception as e:
        messagebox.showerror("启动错误", f"程序启动失败: {str(e)}")
        print(f"启动错误: {e}")
        import traceback
        traceback.print_exc()
